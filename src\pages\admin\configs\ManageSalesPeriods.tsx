import {useEffect, useRef, useState} from "react";
import { useDispatch } from "react-redux";

import {
    getSalesPeriods,
    createSalesPeriod,
    updateSalesPeriod,
    deleteSalesPeriod
} from "../../../features/admin/salesPeriodsSlice";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    message,
    Breadcrumb,
    Row,
    Col,
    DatePicker,
    Tag,
    Select,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import moment from "moment";
import {CheckCircle, ShieldClose} from "lucide-react";
import {getAbnTypesAll} from "../../../features/admin/abnTypeSlice.ts";
import {getCampaignsAll} from "../../../features/admin/campaignSlice.ts";
import {toast} from "react-toastify";

function ManageSalesPeriods() {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;

    const dispatch = useDispatch<any>();
    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [campaignsData, setCampaignsData] = useState<any[]>([]);
    const [abnTypesData, setAbnTypesData] = useState<any[]>([]);
    const [editingSalesPeriod, setEditingSalesPeriod] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    /*|--------------------------------------------------------------------------
   | FETCH ALL SALESPERIODS WITH PAGINATION & FILTER
   |-------------------------------------------------------------------------- */
    const handleGetSalesPeriods = (params: any, sort: any, filter: any) => {
        return dispatch(
            getSalesPeriods({
                pageNumber,
                perPage: pageSize,
                params, sort, filter
            })
        )
            .unwrap()
            .then(async (originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                originalPromiseResult.data.forEach((item: any) => {
                    item.key = item.id;
                });
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
            });
    };

    /*|--------------------------------------------------------------------------
    | FETCH ALL ABN TYPES AND CAMPAIGNS
    |-------------------------------------------------------------------------- */
    useEffect(() => {
        dispatch(getAbnTypesAll())
            .unwrap()
            .then((result: any) => {
                setAbnTypesData(result.data);
            })
            .catch((error: any) => {
                console.error('Error fetching ABN types:', error);
                toast.error(t("messages.error"));
            });

        dispatch(getCampaignsAll())
            .unwrap()
            .then((result: any) => {
                setCampaignsData(result.data);
            })
            .catch((error: any) => {
                console.error('Error fetching tariff bases:', error);
                toast.error(t("messages.error"));
            });
    }, []);

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_salesPeriods.labels.id")}`,
            dataIndex: "id",
            search: false,
            width: 40,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_salesPeriods.labels.name")}`,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_salesPeriods.labels.campaign")}`,
            dataIndex: "campaign",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => {
                return record.campaign ? (
                    <Tag color={record.campaign.color || "default"}>
                        {record.campaign[`nom_${currentLang}`]}
                    </Tag>
                ) : "-";
            },
            valueType: "select",
            fieldProps: {
                showSearch: true,
                placeholder: t("manage_salesPeriods.placeholders.campaign"),
                options: campaignsData.map((campaign: any) => ({
                    label: campaign[`nom_${currentLang}`],
                    value: campaign.id
                })),
                filterOption: (input: string, option?: { label: string; value: number }) =>
                    option?.label.toLowerCase().includes(input.toLowerCase()) ?? false
            }
        },
        {
            title: `${t("manage_salesPeriods.labels.abnType")}`,
            dataIndex: "abn_type",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => {
                return record.abn_type ? (
                    <Tag color={record.abn_type.color || "default"}>
                        {record.abn_type[`nom_${currentLang}`]}
                    </Tag>
                ) : "-";
            },
            valueType: "select",
            fieldProps: {
                showSearch: true,
                placeholder: t("manage_salesPeriods.placeholders.abnType"),
                options: abnTypesData.map((abnType: any) => ({
                    label: abnType[`nom_${currentLang}`],
                    value: abnType.id
                })),
                filterOption: (input: string, option?: { label: string; value: number }) =>
                    option?.label.toLowerCase().includes(input.toLowerCase()) ?? false
            }
        },
        {
            title: t("manage_salesPeriods.labels.period"),
            dataIndex: "period",
            render: (_: any, record: any) => (
                <Tag color={"default"}>
                    {moment(record.date_start).format("DD MMM YYYY")} - {moment(record.date_end).format("DD MMM YYYY")}
                </Tag>
            ),
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t("manage_salesPeriods.labels.status"),
            dataIndex: "status",
            width: 90,
            responsive: ["xs", "sm", "md", "lg"],
            valueType: "select",
            render: (_: any, record: any) => (
                <Tag color={record.status === true ? 'success' : 'error'}>
                    {record.status === true ? t("manage_salesPeriods.open") : t("manage_salesPeriods.closed")}
                </Tag>
            ),
            valueEnum: {
                "1": {
                    text: t("common.active"),
                    status: 'Success'
                },
                "0": {
                    text: t("common.inactive"),
                    status: 'Error'
                }
            },
        },
        {
            title: `${t("manage_salesPeriods.labels.actions")}`,
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    <Button
                        className="btn-edit"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record)}
                    />
                    <Popconfirm
                        title={t("manage_salesPeriods.confirmDelete")}
                        onConfirm={() => handleDelete(record.id)}
                        okText={t("manage_salesPeriods.yes")}
                        cancelText={t("manage_salesPeriods.no")}
                    >
                        <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.sales")}</Link>,
        },
        {
            title: t("manage_salesPeriods.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingSalesPeriod(record);
        console.log(record)
        form.setFieldsValue({
            ...record,
            id_campaign:record.campaign.id,
            id_abn_type: record.abn_type.id,
            start_date: moment(record.date_start),
            end_date: moment(record.date_end),
            status: record.status,
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingSalesPeriod(record);
        form.setFieldsValue({
            ...record,
            id_campaign:record.campaign.id,
            id_abn_type: record.abn_type.id,
            date_start: moment(record.date_start),
            date_end: moment(record.date_end),
            status: record.status,
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingSalesPeriod(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
     | HANDLE SUBMIT
     |-------------------------------------------------------------------------- */
    const handleSalesPeriodSubmit = async (values: any) => {
        setLoading(true);
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });
        try {
            const formattedValues = {
                nom_fr: values.nom_fr,
                nom_en: values.nom_en,
                nom_ar: values.nom_ar,
                date_start: values.date_start ? values.date_start.format('YYYY-MM-DD') : null,
                date_end: values.date_end ? values.date_end.format('YYYY-MM-DD') : null,
                id_campaign: values.id_campaign,
                id_abn_type: values.id_abn_type,
                status: values.status ?? false
            };

            if (editingSalesPeriod) {
                await dispatch(updateSalesPeriod({
                    id: editingSalesPeriod.id,
                    ...formattedValues
                })).unwrap();
            } else {
                await dispatch(createSalesPeriod(formattedValues)).unwrap();
            }

            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });

            handleReset();
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages." + error.message) || t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000,
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_salesPeriods.confirmAction"),
            content: editingSalesPeriod
                ? t("manage_salesPeriods.confirmUpdate")
                : t("manage_salesPeriods.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleSalesPeriodSubmit(values);
            },
            centered: true,
        });
    }

    /*|--------------------------------------------------------------------------
    | - DELETE SALES PERIOD
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        try {
            await dispatch(deleteSalesPeriod(id)).unwrap();
            message.success(t("messages.deleteSuccess"));
            actionRef.current?.reload();
        } catch (error: any) {
            message.error(error.message || t("messages.error"));
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        form.resetFields();
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_salesPeriods.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetSalesPeriods(
                                params,
                                sort,
                                filter
                            );
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{ x: 800 }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_salesPeriods.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_salesPeriods.details")
                        : editingSalesPeriod
                            ? t("manage_salesPeriods.edit")
                            : t("manage_salesPeriods.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_salesPeriods.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_salesPeriods.labels.name_fr")}
                                name="nom_fr"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_salesPeriods.errors.nameFrRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_salesPeriods.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_salesPeriods.labels.name_en")}
                                name="nom_en"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_salesPeriods.errors.nameEnRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_salesPeriods.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_salesPeriods.labels.name_ar")}
                                name="nom_ar"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_salesPeriods.errors.nameArRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_salesPeriods.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={[16,16]}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_salesPeriods.labels.campaign")}
                                name="id_campaign"
                                rules={[{ required: true, message: t("manage_salesPeriods.errors.campaignRequired") }]}
                            >
                                <Select placeholder={t("manage_salesPeriods.placeholders.campaign")} disabled={viewMode}>
                                    {campaignsData.map((el:any) => (
                                        <Select.Option key={el.id} value={el.id}>
                                            {el[`nom_${currentLang}`]}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_salesPeriods.labels.abnType")}
                                name="id_abn_type"
                                rules={[{ required: true, message: t("manage_salesPeriods.errors.abnTypeRequired") }]}
                            >
                                <Select
                                    disabled={viewMode}
                                    placeholder={t("manage_salesPeriods.placeholders.abnType")}
                                >
                                    {abnTypesData.map((type) => (
                                        <Select.Option key={type.id} value={type.id}>
                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                                <div
                                                    style={{
                                                        width: '12px',
                                                        height: '12px',
                                                        borderRadius: '50%',
                                                        backgroundColor: type.color
                                                    }}
                                                />
                                                {type[`nom_${currentLang}`]}
                                            </div>
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>

                     
                    {!viewMode ? (
                        <Row gutter={16}>
                            <Col xs={24} sm={12}>
                                <Form.Item
                                    label={t("manage_salesPeriods.labels.startDate")}
                                    name="date_start"
                                    rules={[{ required: true, message: t("manage_salesPeriods.errors.startDateRequired") }]}
                                >
                                    <DatePicker
                                        className="w-full"
                                        format="YYYY-MM-DD"
                                        disabled={viewMode}
                                        placeholder={t("manage_salesPeriods.placeholders.startDate")}
                                        onChange={(date) => {
                                            if (moment.isMoment(date)) {
                                                form.setFieldsValue({ start_date: date.format("YYYY-MM-DD") });
                                            }
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={12}>
                                <Form.Item
                                    label={t("manage_salesPeriods.labels.endDate")}
                                    name="date_end"
                                    rules={[{ required: true, message: t("manage_salesPeriods.errors.endDateRequired") }]}
                                >
                                    <DatePicker
                                        className="w-full"
                                        format="YYYY-MM-DD"
                                        disabled={viewMode}
                                        placeholder={t("manage_salesPeriods.placeholders.endDate")}
                                        onChange={(date) => {
                                            if (moment.isMoment(date)) {
                                                form.setFieldsValue({ end_date: date.format("YYYY-MM-DD") });
                                            }
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    ) : (
                        <Form.Item label={t("manage_salesPeriods.labels.period")}>
                            <div className="text-center bg-neutral-50 p-4 rounded">
                                <span className="font-semibold" style={{ color: "var(--secondary-color)" }}>
                                    {moment(editingSalesPeriod?.date_start).format("DD MMM YYYY")}
                                </span>
                                {" --- "}
                                <span className="font-semibold" style={{ color: "var(--primary-color)" }}>
                                    {moment(editingSalesPeriod?.date_end).format("DD MMM YYYY")}
                                </span>
                            </div>
                        </Form.Item>
                    )}
                </Form>
            </Modal>
        </>
    );
}

export default ManageSalesPeriods;
